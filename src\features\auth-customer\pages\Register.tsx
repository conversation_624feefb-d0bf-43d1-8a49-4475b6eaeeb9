import CustomerAuthLayout from "@/components/layout/CustomerAuthLayout";
import RegisterForm from "../components/RegisterForm";
import { But<PERSON> } from "@/components/ui/button";
import BrandLogo from "@/assets/logo.png";
import { Link } from "react-router-dom";
function Register() {
  return (
    <CustomerAuthLayout>
      {/* Header */}
      <div className="w-full px-4 sm:px-6 lg:px-10 py-6 flex-shrink-0">
        <div className="flex justify-between items-center gap-4">
          <Link to={"/home"}>
            <img src={BrandLogo} alt="ub sports" className="w-auto h-8" />
          </Link>
          <Link to="/login">
            <Button
              className="px-6 text-center hidden lg:block"
              variant={"default"}
            >
              Member Login
            </Button>
            <span className="block lg:hidden font-bold text-white">
              Member Login
            </span>
          </Link>
        </div>
      </div>

      {/* Main Content - Centered */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-10 pb-8">
        <div className="w-full max-w-lg">
          <RegisterForm />
        </div>
      </div>
    </CustomerAuthLayout>
  );
}

export default Register;
