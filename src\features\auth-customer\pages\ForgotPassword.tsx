import BrandLogo from "@/assets/logo.png";
import CustomerAuthLayout from "@/components/layout/CustomerAuthLayout";
import ForgotPasswordForm from "../components/ForgotPasswordForm";
import { Link } from "react-router-dom";

function ForgotPassword() {
  return (
   <CustomerAuthLayout>
      {/* Header */}
      <div className="flex-shrink-0 w-full px-4 py-6 sm:px-6 lg:px-10">
        <Link to={"/home"} className="flex items-center justify-center lg:justify-normal gap-4">
          <img src={BrandLogo} alt="ub sports" className="w-auto h-8" />
        </Link>
      </div>

      {/* Main Content - Centered */}
      <div className="flex items-center justify-center flex-1 px-4 pb-8 sm:px-6 lg:px-10">
        <div className="w-full max-w-lg">
          <ForgotPasswordForm />
        </div>
      </div>
    </CustomerAuthLayout>  )
}

export default ForgotPassword
