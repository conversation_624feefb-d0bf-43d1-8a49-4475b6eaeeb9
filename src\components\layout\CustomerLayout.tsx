import { customLog } from "@/lib/utils";
import { useBackgroundStore } from "@/store";

function CustomerLayout({ children }: { children: React.ReactNode }) {
  const { backgroundColor,setBackgroundColor } = useBackgroundStore();
 customLog("background color", backgroundColor);

 
  return (
    <div className={`bg-gradient-to-b from-[#CA001A] via-[${backgroundColor}]  to-black min-h-screen w-full flex flex-col`}>
      <div className="grow">{children}</div>
      <footer onClick={()=>setBackgroundColor("#002000")} className="bg-black text-white">
        Footer Content
      </footer>
    </div>
  );
}

export default CustomerLayout;
